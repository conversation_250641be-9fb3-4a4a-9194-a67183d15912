<!--
  *@name feedbackReplyDialog.vue
  *<AUTHOR> Assistant
  *@date 2025/7/29
  *@description 反馈答复Dialog组件
-->
<template>
  <el-dialog
    title="我的反馈"
    :visible.sync="visible"
    v-if="visible"
    width="1500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
    class="feedback-reply-dialog"
  >
    <!-- 搜索条件 -->
    <div class="search-form">
      <el-row :gutter="10">
        <el-col :span="4">
          <div class="form-item">
            <el-input
              v-model="queryList.answerAndQuestion"
              placeholder="请输入问题/回复关键字"
              clearable
            ></el-input>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="form-item">
            <el-input
              v-model="queryList.feedbackContent"
              placeholder="请输入反馈问题关键字"
              clearable
            ></el-input>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="form-item">
            <el-select-multiple
                v-model="queryList.feedbackType"
                placeholder="反馈类型"
                clearable
                confirm>
              <el-select-multiple-option
                  v-for="(item,index) in feedbackTypeOptions"
                  :key="index"
                  :label="item.label"
                  :value="item.value">
              </el-select-multiple-option>
            </el-select-multiple>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="form-item">
            <el-date-picker
              v-model="queryList.createTimeList"
              type="datetimerange"
              :picker-options="pickerOptions"
              start-placeholder="首次提问时间"
              end-placeholder="首次提问时间"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
            ></el-date-picker>
          </div>
        </el-col>
        <el-col :span="4" style="text-align: right;">
          <div class="form-item">
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </div>
        </el-col>
      </el-row>
    </div>
    <!-- 表格 -->
    <div class="table-container">
      <el-table :data="tableData" height="500">
        <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
        <el-table-column prop="question" label="问题&回复" min-width="400">

        </el-table-column>
        <el-table-column label="反馈内容" width="200">
          <template slot-scope="scope">
            <el-tooltip
              :content="scope.row.feedbackContent || '--'"
              placement="top"
              :disabled="!scope.row.feedbackContent || scope.row.feedbackContent === '--'"
              effect="dark">
              <div class="text-ellipsis-3">
                {{ scope.row.feedbackContent ? scope.row.feedbackContent : '--' }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="feedbackType" label="反馈类型" width="100" align="center" :formatter="feedbackTypeFormatter"></el-table-column>
        <el-table-column prop="createTime" label="首次提问时间" width="120" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.createTime" class="time-display">
              <div class="date-line">{{ formatDate(scope.row.createTime) }}</div>
              <div class="time-line">{{ formatTime(scope.row.createTime) }}</div>
            </div>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column prop="newReply" label="最新答复" width="180">
          <template slot-scope="scope">
            <el-tooltip
              :content="scope.row.newReply || '--'"
              placement="top"
              :disabled="!scope.row.newReply || scope.row.newReply === '--'"
              effect="dark">
              <div class="text-ellipsis-3">
                {{ scope.row.newReply ? scope.row.newReply : '--' }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="newReplyTime" label="最新答复时间" width="120" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.newReplyTime" class="time-display">
              <div class="date-line">{{ formatDate(scope.row.newReplyTime) }}</div>
              <div class="time-line">{{ formatTime(scope.row.newReplyTime) }}</div>
            </div>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="110" align="center">
          <template slot-scope="scope">
            <div>历史答复</div>
            <div>继续反馈</div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <el-pagination
        style="text-align: center;"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryList.startRow"
        :page-sizes="[10, 20, 50, 200]"
        :page-size="queryList.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
    </el-pagination>
  </el-dialog>
</template>

<script>
import { _getFeedbackReplyList } from "@/api/chat";

export default {
  name: "feedbackReplyDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      queryList: {
        answerAndQuestion: '',
        feedbackContent: '',
        feedbackTypeList: [],
        createTimeList: [],
        startRow: 1,
        pageSize: 10
      },
      tableData: [],
      total: 0,
      feedbackTypeOptions: [{
        value: '1',
        label: '回答有误'
      }, {
        value: '2',
        label: '响应慢'
      }, {
        value: '3',
        label: '案例有误'
      }, {
        value: '4',
        label: '法规有误'
      }, {
        value: '0',
        label: '其它'
      }],
      pickerOptions: {
        shortcuts: [{
          text: '今天',
          onClick (picker) {
            const start = new Date();
            start.setHours(0, 0, 0, 0); // 设置为当天0点
            const end = new Date();
            end.setHours(23, 59, 59, 999); // 设置为当天23:59:59
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
    }
  },
  created() {
    this.handleReset()
  },
  methods: {
    handleSizeChange(val) {
      this.queryList.pageSize = val
      this.queryList.startRow = 1
      this.getTableData();
    },
    handleCurrentChange(val) {
      this.queryList.startRow = val
      this.getTableData();
    },
    handleSearch() {
      this.queryList.startRow = 1;
      this.getTableData();
    },
    handleReset() {
      this.queryList.answerAndQuestion = ''
      this.queryList.feedbackContent= ''
      this.queryList.feedbackTypeList= []
      this.queryList.createTimeList= []
      this.queryList.startRow= 1
      this.queryList.pageSize= 10
      this.getTableData();
    },
    getTableData() {
      let param = {
        question: this.queryList.answerAndQuestion,
        answer: this.queryList.answerAndQuestion,
        feedbackContent: this.queryList.feedbackContent,
        feedbackTypeList: this.queryList.feedbackTypeList,
        startCreateTime: this.queryList.createTimeList[0],
        endCreateTime: this.queryList.createTimeList[1],
        startRow: this.queryList.startRow,
        pageSize: this.queryList.pageSize
      }
      _getFeedbackReplyList(param).then(res => {
        if (res.data.success) {
          this.tableData = res.data.result.list || [];
          this.total = res.data.result.total || 0;
          
          // 处理反馈类型显示名称
          this.tableData.forEach(item => {
            switch(item.feedbackType) {
              case '1':
                item.feedbackTypeName = '回答有误';
                break;
              case '2':
                item.feedbackTypeName = '响应慢';
                break;
              case '3':
                item.feedbackTypeName = '案例有误';
                break;
              case '4':
                item.feedbackTypeName = '法规有误';
                break;
              case '0':
                item.feedbackTypeName = '其他';
                break;
              default:
                item.feedbackTypeName = '未知';
            }
          });
        }
      })
    },
    feedbackTypeFormatter (row, column, cellValue, index) {
      if (row.feedbackType === '1') {
        return '回答有误';
      } else if (row.feedbackType === '2') {
        return '响应慢';
      } else if (row.feedbackType === '3') {
        return '案例有误';
      } else if (row.feedbackType === '4') {
        return '法规有误';
      } else if (row.feedbackType === '0') {
        return '其他';
      } else {
        return '--';
      }
    },
    formatDate(dateTimeStr) {
      if (!dateTimeStr) return '';
      // 提取年月日部分 (YYYY-MM-DD)
      return dateTimeStr.split(' ')[0];
    },
    formatTime(dateTimeStr) {
      if (!dateTimeStr) return '';
      // 提取时分秒部分 (HH:mm:ss)
      return dateTimeStr.split(' ')[1] || '';
    },
    handleClose() {
      this.$emit('close');
    }
  }
}
</script>

<style scoped lang="scss">
.feedback-reply-dialog {
  .search-form {
    margin-bottom: 10px;
  }

  .table-container {
    margin-bottom: 10px;
  }
}

.text-ellipsis-3 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  max-height: 4.2em; /* 3行 * 1.4行高 */
  word-break: break-word;
}

.time-display {
  .date-line {
    font-size: 13px;
    color: #333;
    line-height: 1.2;
    margin-bottom: 2px;
  }

  .time-line {
    font-size: 12px;
    color: #666;
    line-height: 1.2;
  }
}

::v-deep .el-dialog__body {
  padding: 10px 20px;
}
</style>
